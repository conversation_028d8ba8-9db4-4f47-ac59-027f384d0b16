// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://qngmknkjxwupjtnjgomv.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFuZ21rbmtqeHd1cGp0bmpnb212Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2NTYzODksImV4cCI6MjA2NDIzMjM4OX0.aDlKxsxZZR_7SFPpriZ-Qem128V3ZhXeYMy0YLUg5iM";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);