
export const TestimonialsSection = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "CTO at TechCorp",
      content: "TechHub has revolutionized how we approach development. The platform is intuitive and powerful.",
      avatar: "<PERSON><PERSON>",
    },
    {
      name: "<PERSON>",
      role: "Lead Developer",
      content: "The analytics and monitoring tools have saved us countless hours. Highly recommended!",
      avatar: "<PERSON>",
    },
    {
      name: "<PERSON>",
      role: "Product Manager",
      content: "Excellent customer support and amazing features. TechHub is a game-changer.",
      avatar: "ER",
    },
  ];

  return (
    <section id="testimonials" className="py-20 bg-white">
      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            What Our Users Say
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what real users have to say about TechHub.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-gray-50 rounded-2xl p-8">
              <p className="text-gray-700 mb-6 leading-relaxed italic">
                "{testimonial.content}"
              </p>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-[#4ADE80] rounded-full flex items-center justify-center text-white font-bold mr-4">
                  {testimonial.avatar}
                </div>
                <div>
                  <h4 className="font-bold text-gray-900">{testimonial.name}</h4>
                  <p className="text-gray-600 text-sm">{testimonial.role}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
