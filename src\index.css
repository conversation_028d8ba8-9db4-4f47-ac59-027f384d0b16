
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.geist-ui.dev/font.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 4%;
    --foreground: 0 0% 100%;
    --card: 0 0% 4%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 4%;
    --popover-foreground: 0 0% 100%;
    --primary: 142 84% 58%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 6%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 6%;
    --muted-foreground: 0 0% 64%;
    --accent: 0 0% 6%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 12%;
    --input: 0 0% 6%;
    --ring: 142 84% 58%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 4%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 142 84% 58%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 0 0% 6%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 0 0% 12%;
    --sidebar-ring: 142 84% 58%;
  }

  .dark {
    --background: 0 0% 4%;
    --foreground: 0 0% 100%;
    --card: 0 0% 4%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 4%;
    --popover-foreground: 0 0% 100%;
    --primary: 142 84% 58%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 6%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 6%;
    --muted-foreground: 0 0% 64%;
    --accent: 0 0% 6%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 12%;
    --input: 0 0% 6%;
    --ring: 142 84% 58%;
    --sidebar-background: 0 0% 4%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 142 84% 58%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 0 0% 6%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 0 0% 12%;
    --sidebar-ring: 142 84% 58%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
}

@layer components {
  .button-gradient {
    @apply bg-gradient-to-r from-[#4ADE80] to-[#22C55E] hover:from-[#22C55E] hover:to-[#16A34A] transition-all;
  }

  .glass {
    @apply bg-white/5 backdrop-blur-lg;
    border: 1px solid transparent;
    background-clip: padding-box;
    position: relative;
  }

  .glass::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(225deg, rgba(74, 222, 128, 0.2), rgba(74, 222, 128, 0.05));
    -webkit-mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
    mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }
}
